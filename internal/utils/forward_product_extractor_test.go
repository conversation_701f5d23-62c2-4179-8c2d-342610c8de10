package utils

import (
	"testing"

	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewProductExtractor(t *testing.T) {
	extractor := NewProductExtractor()

	assert.NotNil(t, extractor)
	assert.NotNil(t, extractor.coreExtractor)
}

func TestProductExtractor_ExtractFromMessage(t *testing.T) {
	extractor := NewProductExtractor()

	tests := []struct {
		name     string
		message  *discordgo.Message
		expected int // 期望的产品数量
	}{
		{
			name: "消息只有内容，无embeds",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "这是一个测试产品",
				Author: &discordgo.User{
					ID:       "user123",
					Username: "testuser",
				},
			},
			expected: 1,
		},
		{
			name: "消息有embeds",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "产品信息:",
				Author: &discordgo.User{
					ID:       "user123",
					Username: "testuser",
				},
				Embeds: []*discordgo.MessageEmbed{
					{
						Title:       "测试产品",
						Description: "这是一个测试产品描述",
						URL:         "https://example.com/product/123",
						Color:       0xFF0000,
						Fields: []*discordgo.MessageEmbedField{
							{
								Name:  "价格",
								Value: "$19.99",
							},
							{
								Name:  "ProductID",
								Value: "PROD123",
							},
						},
					},
				},
			},
			expected: 1,
		},
		{
			name: "空消息",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "",
				Author: &discordgo.User{
					ID:       "user123",
					Username: "testuser",
				},
			},
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			products, err := extractor.ExtractFromMessage(tt.message)
			require.NoError(t, err)
			assert.Len(t, products, tt.expected)

			if len(products) > 0 {
				product := products[0]
				assert.NotNil(t, product)
				assert.NotNil(t, product.Metadata)
			}
		})
	}
}

func TestProductExtractor_ExtractFromEmbedData(t *testing.T) {
	extractor := NewProductExtractor()

	embedData := map[string]interface{}{
		"title":       "测试产品",
		"description": "产品描述",
		"url":         "https://example.com",
		"color":       16711680, // 0xFF0000
		"image": map[string]interface{}{
			"url": "https://example.com/image.jpg",
		},
		"author": map[string]interface{}{
			"name": "测试作者",
			"url":  "https://example.com/author",
		},
		"fields": []interface{}{
			map[string]interface{}{
				"name":  "价格",
				"value": "$19.99",
			},
			map[string]interface{}{
				"name":  "ProductID",
				"value": "PROD123",
			},
		},
	}

	result := extractor.ExtractFromEmbedData(embedData)

	assert.Equal(t, "测试产品", result["Title"])
	assert.Equal(t, "产品描述", result["Description"])
	assert.Equal(t, "https://example.com", result["URL"])
	assert.Equal(t, int64(16711680), result["Color"])
	assert.Equal(t, "https://example.com/image.jpg", result["ImageURL"])
	assert.Equal(t, "测试作者", result["AuthorName"])
	assert.Equal(t, "https://example.com/author", result["AuthorURL"])
	assert.Equal(t, "$19.99", result["价格"])
	assert.Equal(t, "PROD123", result["ProductID"])
}

func TestProductExtractor_CreateProductFromExtractedData(t *testing.T) {
	extractor := NewProductExtractor()

	tests := []struct {
		name          string
		extractedData map[string]interface{}
		expectNil     bool
		checkFields   func(*testing.T, *types.ProductItem)
	}{
		{
			name: "完整产品数据",
			extractedData: map[string]interface{}{
				"Title":       "测试产品",
				"URL":         "https://example.com",
				"ProductID":   "PROD123",
				"Price":       "$19.99",
				"Description": "产品描述",
				"Color":       int64(16711680),
				"ImageURL":    "https://example.com/image.jpg",
				"AuthorName":  "测试作者",
				"CustomField": "自定义值",
			},
			expectNil: false,
			checkFields: func(t *testing.T, product *types.ProductItem) {
				assert.Equal(t, "测试产品", product.Title)
				assert.Equal(t, "https://example.com", product.URL)
				assert.Equal(t, "PROD123", product.ProductID)
				assert.Equal(t, "$19.99", product.Price)
				assert.Equal(t, "产品描述", product.Description)
				assert.Equal(t, 16711680, product.Color)
				assert.Equal(t, "https://example.com/image.jpg", product.ImageURL)
				assert.Equal(t, "测试作者", product.AuthorName)
				assert.Equal(t, "自定义值", product.Metadata["CustomField"])
			},
		},
		{
			name:          "空数据",
			extractedData: map[string]interface{}{},
			expectNil:     true,
		},
		{
			name: "只有非关键字段",
			extractedData: map[string]interface{}{
				"CustomField": "值",
			},
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			product := extractor.CreateProductFromExtractedData(tt.extractedData)

			if tt.expectNil {
				assert.Nil(t, product)
			} else {
				require.NotNil(t, product)
				assert.NotNil(t, product.Metadata)
				assert.Equal(t, 0, product.Stock)
				assert.Equal(t, "unknown", product.Availability)

				if tt.checkFields != nil {
					tt.checkFields(t, product)
				}
			}
		})
	}
}

func TestProductExtractor_ConvertDiscordMessageToMap(t *testing.T) {
	extractor := NewProductExtractor()

	message := &discordgo.Message{
		ID:        "123456789",
		ChannelID: "987654321",
		GuildID:   "guild123",
		Content:   "测试消息",
		Author: &discordgo.User{
			ID:       "user123",
			Username: "testuser",
		},
		Embeds: []*discordgo.MessageEmbed{
			{
				Title:       "测试Embed",
				Description: "测试描述",
				URL:         "https://example.com",
				Color:       0xFF0000,
			},
		},
	}

	result := extractor.ConvertDiscordMessageToMap(message)

	assert.Equal(t, "测试消息", result["content"])
	assert.Equal(t, "user123", result["author_id"])
	assert.Equal(t, "testuser", result["author"])
	assert.Equal(t, "987654321", result["channel_id"])
	assert.Equal(t, "guild123", result["guild_id"])
	assert.Equal(t, "123456789", result["message_id"])

	embeds, ok := result["embeds"].([]map[string]interface{})
	require.True(t, ok)
	require.Len(t, embeds, 1)

	embed := embeds[0]
	assert.Equal(t, "测试Embed", embed["title"])
	assert.Equal(t, "测试描述", embed["description"])
	assert.Equal(t, "https://example.com", embed["url"])
	assert.Equal(t, 0xFF0000, embed["color"])
}

func TestProductExtractor_ConvertEmbedToMap(t *testing.T) {
	extractor := NewProductExtractor()

	embed := &discordgo.MessageEmbed{
		Title:       "测试Embed",
		Description: "测试描述",
		URL:         "https://example.com",
		Color:       0xFF0000,
		Image: &discordgo.MessageEmbedImage{
			URL: "https://example.com/image.jpg",
		},
		Author: &discordgo.MessageEmbedAuthor{
			Name: "测试作者",
			URL:  "https://example.com/author",
		},
		Fields: []*discordgo.MessageEmbedField{
			{
				Name:   "字段1",
				Value:  "值1",
				Inline: true,
			},
		},
	}

	result := extractor.ConvertEmbedToMap(embed)

	assert.Equal(t, "测试Embed", result["title"])
	assert.Equal(t, "测试描述", result["description"])
	assert.Equal(t, "https://example.com", result["url"])
	assert.Equal(t, 0xFF0000, result["color"])

	image, ok := result["image"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "https://example.com/image.jpg", image["url"])

	author, ok := result["author"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "测试作者", author["name"])
	assert.Equal(t, "https://example.com/author", author["url"])

	fields, ok := result["fields"].([]map[string]interface{})
	require.True(t, ok)
	require.Len(t, fields, 1)

	field := fields[0]
	assert.Equal(t, "字段1", field["name"])
	assert.Equal(t, "值1", field["value"])
	assert.Equal(t, true, field["inline"])
}

func TestProductExtractor_IsStandardField(t *testing.T) {
	extractor := NewProductExtractor()

	tests := []struct {
		fieldName string
		expected  bool
	}{
		{"Title", true},
		{"URL", true},
		{"ProductID", true},
		{"CustomField", false},
		{"价格", false},
		{"Description", true},
		{"Metadata", false},
	}

	for _, tt := range tests {
		t.Run(tt.fieldName, func(t *testing.T) {
			result := extractor.isStandardField(tt.fieldName)
			assert.Equal(t, tt.expected, result)
		})
	}
}
