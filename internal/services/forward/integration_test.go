package forward

import (
	"testing"

	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestForwardRuleService_ProductExtractorIntegration 测试重构后的产品信息提取功能
func TestForwardRuleService_ProductExtractorIntegration(t *testing.T) {
	// 创建转发规则服务
	service := NewForwardRuleService("test_config.yaml")

	// 验证产品提取器已正确初始化
	assert.NotNil(t, service.productExtractor)

	// 创建测试消息
	message := &discordgo.Message{
		ID:        "123456789",
		ChannelID: "987654321",
		Content:   "产品信息",
		Author: &discordgo.User{
			ID:       "user123",
			Username: "testuser",
		},
		Embeds: []*discordgo.MessageEmbed{
			{
				Title:       "测试产品",
				Description: "这是一个测试产品",
				URL:         "https://example.com/product/123",
				Color:       0xFF0000,
				Fields: []*discordgo.MessageEmbedField{
					{
						Name:  "价格",
						Value: "$19.99",
					},
					{
						Name:  "ProductID",
						Value: "PROD123",
					},
				},
			},
		},
	}

	// 创建测试规则
	rule := &types.ForwardRule{
		Name:              "test_rule",
		SourceChannelID:   "987654321",
		TargetChannelID:   "123456789",
		Enabled:           true,
		FieldMappingGroup: "default",
	}

	// 测试产品信息提取（无映射服务）
	content, embeds, products := service.processMapping(message, rule)

	// 验证结果
	assert.Equal(t, "产品信息", content)
	assert.Len(t, embeds, 1)
	require.Len(t, products, 1)

	product := products[0]
	assert.Equal(t, "测试产品", product.Title)
	assert.Equal(t, "https://example.com/product/123", product.URL)
	assert.Equal(t, "这是一个测试产品", product.Description)
	assert.Equal(t, 16711680, product.Color) // 0xFF0000

	// 检查字段是否被正确提取
	assert.Contains(t, product.Metadata, "价格")
	assert.Equal(t, "$19.99", product.Metadata["价格"])

	// ProductID 应该被映射到产品的 ProductID 字段，而不是 Metadata
	// 但如果没有被识别，也可能在 Metadata 中
	if product.ProductID != "" {
		assert.Equal(t, "PROD123", product.ProductID)
	} else {
		// 如果没有被识别为 ProductID，检查是否在 Metadata 中
		assert.Contains(t, product.Metadata, "ProductID")
		assert.Equal(t, "PROD123", product.Metadata["ProductID"])
	}
}

// TestForwardRuleService_ConvertDiscordMessageToMap 测试消息转换功能
func TestForwardRuleService_ConvertDiscordMessageToMap(t *testing.T) {
	service := NewForwardRuleService("test_config.yaml")

	message := &discordgo.Message{
		ID:        "123456789",
		ChannelID: "987654321",
		Content:   "测试消息",
		Author: &discordgo.User{
			ID:       "user123",
			Username: "testuser",
		},
		Embeds: []*discordgo.MessageEmbed{
			{
				Title: "测试Embed",
				URL:   "https://example.com",
			},
		},
	}

	// 使用新的产品提取器进行转换
	result := service.productExtractor.ConvertDiscordMessageToMap(message)

	assert.Equal(t, "测试消息", result["content"])
	assert.Equal(t, "user123", result["author_id"])
	assert.Equal(t, "testuser", result["author"])
	assert.Equal(t, "987654321", result["channel_id"])
	assert.Equal(t, "123456789", result["message_id"])

	embeds, ok := result["embeds"].([]map[string]interface{})
	require.True(t, ok)
	require.Len(t, embeds, 1)

	embed := embeds[0]
	assert.Equal(t, "测试Embed", embed["title"])
	assert.Equal(t, "https://example.com", embed["url"])
}

// TestForwardRuleService_ExtractFromMessage 测试直接从消息提取产品信息
func TestForwardRuleService_ExtractFromMessage(t *testing.T) {
	service := NewForwardRuleService("test_config.yaml")

	tests := []struct {
		name     string
		message  *discordgo.Message
		expected int // 期望的产品数量
	}{
		{
			name: "带embed的消息",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "产品信息",
				Author: &discordgo.User{
					ID:       "user123",
					Username: "testuser",
				},
				Embeds: []*discordgo.MessageEmbed{
					{
						Title: "产品标题",
						URL:   "https://example.com/product",
						Fields: []*discordgo.MessageEmbedField{
							{
								Name:  "价格",
								Value: "$29.99",
							},
						},
					},
				},
			},
			expected: 1,
		},
		{
			name: "只有内容的消息",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "简单产品信息",
				Author: &discordgo.User{
					ID:       "user123",
					Username: "testuser",
				},
			},
			expected: 1,
		},
		{
			name: "空消息",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "",
				Author: &discordgo.User{
					ID:       "user123",
					Username: "testuser",
				},
			},
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			products, err := service.productExtractor.ExtractFromMessage(tt.message)
			require.NoError(t, err)
			assert.Len(t, products, tt.expected)

			if len(products) > 0 {
				product := products[0]
				assert.NotNil(t, product)
				assert.NotNil(t, product.Metadata)
				assert.Equal(t, 0, product.Stock)
				assert.Equal(t, "unknown", product.Availability)
			}
		})
	}
}

// TestForwardRuleService_BackwardCompatibility 测试向后兼容性
func TestForwardRuleService_BackwardCompatibility(t *testing.T) {
	service := NewForwardRuleService("test_config.yaml")

	// 验证服务的基本属性没有改变
	assert.Equal(t, "ForwardRuleService", service.GetName())
	assert.Equal(t, "forward", service.GetType())
	assert.Contains(t, service.GetDependencies(), "FieldMappingService")

	// 验证产品提取器已正确初始化
	assert.NotNil(t, service.productExtractor)

	// 验证其他字段仍然存在
	assert.NotNil(t, service.rules)
	assert.NotNil(t, service.sourceChannelIndex)
	assert.NotNil(t, service.targetChannelIndex)
	assert.NotNil(t, service.stats)
}
