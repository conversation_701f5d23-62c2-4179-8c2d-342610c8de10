package forward

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/template"
	"zeka-go/internal/tasks/message_forward"
	"zeka-go/internal/types"
	"zeka-go/internal/utils"

	"github.com/bwmarrin/discordgo"
	"github.com/google/uuid"
	"gopkg.in/yaml.v3"
)

// ForwardRuleService 转发规则服务 - 简化重构版本
type ForwardRuleService struct {
	// 基础信息
	name         string
	serviceType  string
	dependencies []string

	// 配置和状态
	configFile string
	config     *types.ForwardRulesConfig
	rules      map[string]*types.ForwardRule
	stats      map[string]*types.ForwardRuleStats
	isRunning  bool

	// 索引
	sourceChannelIndex map[string][]*types.ForwardRule
	targetChannelIndex map[string][]*types.ForwardRule

	// 并发控制
	mu     sync.RWMutex
	ctx    context.Context
	cancel context.CancelFunc

	// 外部依赖（运行时注入）
	queueService     types.QueueService
	mappingService   types.FieldMapper
	filterService    types.FilterEngine
	templateManager  *template.Manager
	templateRenderer *template.Renderer
	channelCache     ChannelNameCacheInterface
	discordClient    *types.Client

	// 产品信息提取器
	productExtractor *utils.ProductExtractor
}

// ChannelNameCacheInterface 频道名称缓存接口
type ChannelNameCacheInterface interface {
	GetChannelName(channelID string) (string, error)
}

// NewForwardRuleService 创建转发规则服务实例
func NewForwardRuleService(configFile string) *ForwardRuleService {
	ctx, cancel := context.WithCancel(context.Background())

	return &ForwardRuleService{
		name:               "ForwardRuleService",
		serviceType:        "forward",
		dependencies:       []string{"FieldMappingService"},
		configFile:         configFile,
		rules:              make(map[string]*types.ForwardRule),
		sourceChannelIndex: make(map[string][]*types.ForwardRule),
		targetChannelIndex: make(map[string][]*types.ForwardRule),
		stats:              make(map[string]*types.ForwardRuleStats),
		ctx:                ctx,
		cancel:             cancel,
		productExtractor:   utils.NewProductExtractor(),
	}
}

// Initialize 初始化服务
func (frs *ForwardRuleService) Initialize(ctx context.Context) error {
	logger.Info("初始化转发规则服务", "config_file", frs.configFile)

	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	if err := frs.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	frs.buildIndexes()

	logger.Info("转发规则服务初始化完成",
		"rules_count", len(frs.rules),
		"enabled_rules", frs.countEnabledRules())

	return nil
}

// Start 启动服务
func (frs *ForwardRuleService) Start(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if frs.isRunning {
		return fmt.Errorf("服务已在运行")
	}

	frs.isRunning = true
	logger.Info("转发规则服务已启动")
	return nil
}

// Stop 停止服务
func (frs *ForwardRuleService) Stop(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("服务未运行")
	}

	frs.isRunning = false
	frs.cancel()
	logger.Info("转发规则服务已停止")
	return nil
}

// HealthCheck 健康检查
func (frs *ForwardRuleService) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	result := &services.HealthCheckResult{
		Healthy:   frs.isRunning,
		CheckTime: time.Now(),
	}

	if !frs.isRunning {
		result.Message = "服务未运行"
	} else {
		result.Message = "服务运行正常"
	}

	return result
}

// GetName 获取服务名称
func (frs *ForwardRuleService) GetName() string {
	return frs.name
}

// GetType 获取服务类型
func (frs *ForwardRuleService) GetType() string {
	return frs.serviceType
}

// GetDependencies 获取服务依赖
func (frs *ForwardRuleService) GetDependencies() []string {
	return frs.dependencies
}

// 依赖注入方法
func (frs *ForwardRuleService) SetQueueService(queueService types.QueueService) {
	frs.queueService = queueService
	logger.Debug("队列服务已注入")
}

func (frs *ForwardRuleService) SetMappingService(mappingService types.FieldMapper) {
	frs.mappingService = mappingService
	logger.Debug("字段映射服务已注入")
}

func (frs *ForwardRuleService) SetFilterService(filterService types.FilterEngine) {
	frs.filterService = filterService
	logger.Debug("过滤服务已注入")
}

func (frs *ForwardRuleService) SetChannelCache(channelCache ChannelNameCacheInterface) {
	frs.channelCache = channelCache
	logger.Debug("频道缓存已注入")
}

func (frs *ForwardRuleService) InjectTemplateServices(manager *template.Manager, renderer *template.Renderer) {
	frs.templateManager = manager
	frs.templateRenderer = renderer
	logger.Debug("模板服务已注入")
}

// SetDiscordClient 设置Discord客户端
func (frs *ForwardRuleService) SetDiscordClient(client *types.Client) {
	frs.discordClient = client
	logger.Debug("Discord客户端已设置到转发服务")
}

// 核心转发方法
func (frs *ForwardRuleService) ForwardMessage(rule *types.ForwardRule, message interface{}) error {
	if !frs.isRunning {
		return fmt.Errorf("服务未运行")
	}

	// 转换消息格式
	discordMessage, err := frs.convertToDiscordMessage(message)
	if err != nil {
		return fmt.Errorf("消息格式转换失败: %w", err)
	}

	// 处理字段映射
	mappedContent, mappedEmbeds, productItems := frs.processMapping(discordMessage, rule)

	// 应用过滤器
	if frs.filterService != nil && !frs.shouldAllowMessage(productItems, rule) {
		logger.Debug("消息被过滤器拦截", "rule", rule.Name)
		return nil
	}

	// 创建并发布转发任务
	taskData, err := frs.createSimpleTask(rule, discordMessage, mappedContent, mappedEmbeds, productItems)
	if err != nil {
		return fmt.Errorf("创建转发任务失败: %w", err)
	}

	if frs.queueService != nil {
		taskID, err := frs.publishToQueue(taskData)
		if err != nil {
			return fmt.Errorf("发布任务失败: %w", err)
		}
		logger.Debug("转发任务已发布", "task_id", taskID, "rule", rule.Name)
	}

	// 更新统计
	frs.updateStats(rule.Name, true)

	return nil
}

// GetRulesBySourceChannel 根据源频道获取规则
func (frs *ForwardRuleService) GetRulesBySourceChannel(channelID string) []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	if rules, exists := frs.sourceChannelIndex[channelID]; exists {
		result := make([]*types.ForwardRule, len(rules))
		copy(result, rules)
		return result
	}

	return nil
}

// GetRulesByTargetChannel 根据目标频道获取规则
func (frs *ForwardRuleService) GetRulesByTargetChannel(channelID string) []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	if rules, exists := frs.targetChannelIndex[channelID]; exists {
		result := make([]*types.ForwardRule, len(rules))
		copy(result, rules)
		return result
	}

	return nil
}

// AddRule 添加转发规则
func (frs *ForwardRuleService) AddRule(rule *types.ForwardRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	if _, exists := frs.rules[rule.Name]; exists {
		return fmt.Errorf("规则 %s 已存在", rule.Name)
	}

	frs.rules[rule.Name] = rule
	frs.stats[rule.Name] = &rule.Stats
	frs.config.ForwardRules = append(frs.config.ForwardRules, *rule)
	frs.buildIndexes()

	logger.Info("转发规则已添加", "rule", rule.Name)
	return nil
}

// RemoveRule 移除转发规则
func (frs *ForwardRuleService) RemoveRule(ruleName string) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if _, exists := frs.rules[ruleName]; !exists {
		return fmt.Errorf("规则 %s 不存在", ruleName)
	}

	delete(frs.rules, ruleName)
	delete(frs.stats, ruleName)

	// 从配置中删除
	for i, rule := range frs.config.ForwardRules {
		if rule.Name == ruleName {
			frs.config.ForwardRules = append(frs.config.ForwardRules[:i], frs.config.ForwardRules[i+1:]...)
			break
		}
	}

	frs.buildIndexes()
	logger.Info("转发规则已删除", "rule", ruleName)
	return nil
}

// UpdateRule 更新转发规则
func (frs *ForwardRuleService) UpdateRule(rule *types.ForwardRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	if _, exists := frs.rules[rule.Name]; !exists {
		return fmt.Errorf("规则 %s 不存在", rule.Name)
	}

	frs.rules[rule.Name] = rule
	frs.stats[rule.Name] = &rule.Stats

	// 更新配置
	for i, configRule := range frs.config.ForwardRules {
		if configRule.Name == rule.Name {
			frs.config.ForwardRules[i] = *rule
			break
		}
	}

	frs.buildIndexes()
	logger.Info("转发规则已更新", "rule", rule.Name)
	return nil
}

// GetRule 获取转发规则
func (frs *ForwardRuleService) GetRule(ruleName string) (*types.ForwardRule, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rule, exists := frs.rules[ruleName]
	if !exists {
		return nil, fmt.Errorf("规则 %s 不存在", ruleName)
	}

	// 返回副本
	ruleCopy := *rule
	return &ruleCopy, nil
}

// ListRules 列出所有转发规则
func (frs *ForwardRuleService) ListRules() []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rules := make([]*types.ForwardRule, 0, len(frs.rules))
	for _, rule := range frs.rules {
		ruleCopy := *rule
		rules = append(rules, &ruleCopy)
	}

	return rules
}

// GetRuleStats 获取规则统计信息
func (frs *ForwardRuleService) GetRuleStats(ruleName string) (*types.ForwardRuleStats, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	stats, exists := frs.stats[ruleName]
	if !exists {
		return nil, fmt.Errorf("规则 %s 的统计信息不存在", ruleName)
	}

	// 返回副本
	statsCopy := *stats
	return &statsCopy, nil
}

// UpdateRuleStats 更新规则统计信息
func (frs *ForwardRuleService) UpdateRuleStats(ruleName string, stats *types.ForwardRuleStats) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if _, exists := frs.stats[ruleName]; !exists {
		return fmt.Errorf("规则 %s 的统计信息不存在", ruleName)
	}

	frs.stats[ruleName] = stats
	return nil
}

// ShouldForward 检查是否应该转发消息
func (frs *ForwardRuleService) ShouldForward(rule *types.ForwardRule, message interface{}) (bool, error) {
	return rule.Enabled, nil
}

// ReloadConfig 重新加载配置
func (frs *ForwardRuleService) ReloadConfig() error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	logger.Info("重新加载转发规则配置")

	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	if err := frs.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	frs.buildIndexes()

	logger.Info("转发规则配置重新加载完成",
		"rules_count", len(frs.rules),
		"enabled_rules", frs.countEnabledRules())

	return nil
}

// 辅助方法

// loadConfig 加载配置文件
func (frs *ForwardRuleService) loadConfig() error {
	data, err := os.ReadFile(frs.configFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 尝试新格式
	newConfig := &types.ForwardRulesMainConfig{}
	if err := yaml.Unmarshal(data, newConfig); err == nil && newConfig.ForwardRules.Rules != nil {
		frs.config = &types.ForwardRulesConfig{
			ForwardRules:   newConfig.ForwardRules.Rules,
			GlobalSettings: newConfig.GlobalSettings,
		}
	} else {
		// 回退到旧格式
		config := &types.ForwardRulesConfig{}
		if err := yaml.Unmarshal(data, config); err != nil {
			return fmt.Errorf("解析配置文件失败: %w", err)
		}
		frs.config = config
	}

	// 加载规则到内存
	frs.rules = make(map[string]*types.ForwardRule)
	frs.stats = make(map[string]*types.ForwardRuleStats)

	for i := range frs.config.ForwardRules {
		rule := &frs.config.ForwardRules[i]

		// 生成规则名称（如果为空）
		if rule.Name == "" {
			rule.Name = fmt.Sprintf("%s_to_%s", rule.InputChannel, rule.OutputChannel)
		}

		// 同步新旧字段名
		if rule.InputChannel != "" && rule.SourceChannelID == "" {
			rule.SourceChannelID = rule.InputChannel
		}
		if rule.OutputChannel != "" && rule.TargetChannelID == "" {
			rule.TargetChannelID = rule.OutputChannel
		}

		// 验证规则
		if err := rule.Validate(); err != nil {
			logger.Error("规则验证失败，跳过", "rule", rule.Name, "error", err)
			continue
		}

		frs.rules[rule.Name] = rule
		frs.stats[rule.Name] = &rule.Stats
	}

	return nil
}

// validateConfig 验证配置
func (frs *ForwardRuleService) validateConfig() error {
	if frs.config == nil {
		return fmt.Errorf("配置为空")
	}

	if len(frs.config.ForwardRules) == 0 {
		return fmt.Errorf("没有配置转发规则")
	}

	return nil
}

// buildIndexes 构建索引
func (frs *ForwardRuleService) buildIndexes() {
	frs.sourceChannelIndex = make(map[string][]*types.ForwardRule)
	frs.targetChannelIndex = make(map[string][]*types.ForwardRule)

	for _, rule := range frs.rules {
		if rule.Enabled {
			sourceID := rule.GetSourceChannelID()
			targetID := rule.GetTargetChannelID()

			frs.sourceChannelIndex[sourceID] = append(frs.sourceChannelIndex[sourceID], rule)
			frs.targetChannelIndex[targetID] = append(frs.targetChannelIndex[targetID], rule)
		}
	}
}

// countEnabledRules 统计启用的规则数量
func (frs *ForwardRuleService) countEnabledRules() int {
	count := 0
	for _, rule := range frs.rules {
		if rule.Enabled {
			count++
		}
	}
	return count
}

// convertToDiscordMessage 转换消息为Discord消息格式
func (frs *ForwardRuleService) convertToDiscordMessage(message interface{}) (*discordgo.Message, error) {
	switch msg := message.(type) {
	case *discordgo.Message:
		return msg, nil
	case discordgo.Message:
		return &msg, nil
	case *discordgo.MessageCreate:
		// MessageCreate 包含一个 Message 字段
		return msg.Message, nil
	case discordgo.MessageCreate:
		// MessageCreate 包含一个 Message 字段
		return msg.Message, nil
	default:
		return nil, fmt.Errorf("不支持的消息类型: %T", message)
	}
}

// processMapping 处理字段映射（使用FieldMappingService）
func (frs *ForwardRuleService) processMapping(message *discordgo.Message, rule *types.ForwardRule) (string, []*discordgo.MessageEmbed, []*types.ProductItem) {
	// 使用统一的产品提取器进行高级映射
	result := frs.productExtractor.ExtractWithMapping(message, frs.mappingService, rule.FieldMappingGroup)

	// 记录映射过程中的错误和警告
	for _, warning := range result.Warnings {
		logger.Warn("产品映射警告", "warning", warning, "rule", rule.Name)
	}
	for _, errMsg := range result.Errors {
		logger.Error("产品映射错误", "error", errMsg, "rule", rule.Name)
	}

	logger.Debug("产品映射完成",
		"rule", rule.Name,
		"mapping_group", result.MappingGroup,
		"products_count", len(result.Products),
		"errors_count", len(result.Errors),
		"warnings_count", len(result.Warnings))

	return result.Content, result.Embeds, result.Products
}

// shouldAllowMessage 检查消息是否应该被允许
func (frs *ForwardRuleService) shouldAllowMessage(productItems []*types.ProductItem, rule *types.ForwardRule) bool {
	if frs.filterService == nil {
		return true
	}

	// 检查产品过滤（新增：使用完整的产品信息进行过滤）
	for i, product := range productItems {
		if product != nil {
			result, err := frs.filterService.CheckProduct(rule.GetTargetChannelID(), product)
			if err != nil {
				logger.Error("产品过滤器检查失败", "error", err, "rule", rule.Name, "product_index", i)
				continue // 过滤器错误时继续检查下一个产品
			}
			if !result.Allowed {
				logger.Debug("产品被过滤器拦截",
					"rule", rule.Name,
					"reason", result.Reason,
					"product_id", product.ProductID,
					"product_title", product.Title,
					"product_url", product.URL)
				return false
			}
		}
	}

	return true
}

// createSimpleTask 创建简化的转发任务并返回JSON格式
func (frs *ForwardRuleService) createSimpleTask(rule *types.ForwardRule, message *discordgo.Message, content string, _ []*discordgo.MessageEmbed, products []*types.ProductItem) ([]byte, error) {
	// 创建 MessageForwardTask 对象
	task := &message_forward.MessageForwardTask{
		ID:              uuid.New().String(),
		OriginalMessage: content,
		SourceChannel:   rule.GetSourceChannelID(),
		TargetChannels:  []string{rule.GetTargetChannelID()},
		AuthorID:        message.Author.ID,
		AuthorName:      message.Author.Username,
		GuildID:         message.GuildID,
		MessageID:       message.ID,
		CreatedAt:       time.Now(),
		MappingName:     rule.FieldMappingGroup,
		Products:        products,
		DelaySeconds:    rule.ForwardConfig.DelaySeconds,
	}

	// 序列化为JSON格式
	jsonData, err := json.Marshal(task)
	if err != nil {
		return nil, fmt.Errorf("序列化转发任务失败: %w", err)
	}

	return jsonData, nil
}

// publishToQueue 发布任务到队列或直接发送
func (frs *ForwardRuleService) publishToQueue(taskData []byte) (string, error) {
	// 将JSON字节数组解析为MessageForwardTask
	var task message_forward.MessageForwardTask
	if err := json.Unmarshal(taskData, &task); err != nil {
		return "", fmt.Errorf("解析任务数据失败: %w", err)
	}

	// 检查是否需要延迟发布
	if task.DelaySeconds > 0 {
		// 延迟任务：使用队列
		return frs.publishDelayedTask(&task)
	} else {
		// 立即任务：直接发送
		return frs.sendImmediateTask(&task)
	}
}

// publishDelayedTask 发布延迟任务到队列
func (frs *ForwardRuleService) publishDelayedTask(task *message_forward.MessageForwardTask) (string, error) {
	if frs.queueService == nil {
		return "", fmt.Errorf("队列服务未配置")
	}

	// 使用队列服务发布延迟任务
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	publishOptions := types.PublishOptions{
		Priority:  0,
		TTL:       24 * time.Hour, // 24小时过期
		Mandatory: false,
		Immediate: false,
	}

	delay := time.Duration(task.DelaySeconds) * time.Second
	taskID, err := frs.queueService.PublishDelayedTask(ctx, "message_forward", "message_forward", task, delay, publishOptions)

	if err != nil {
		return "", fmt.Errorf("发布延迟任务到队列失败: %w", err)
	}

	logger.Debug("发布延迟转发任务", "delay_seconds", task.DelaySeconds, "task_id", taskID)
	return taskID, nil
}

// sendImmediateTask 立即发送任务
func (frs *ForwardRuleService) sendImmediateTask(task *message_forward.MessageForwardTask) (string, error) {
	// 使用上下文进行超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 直接发送消息
	if err := frs.sendMessageDirectly(ctx, task); err != nil {
		return "", fmt.Errorf("立即发送消息失败: %w", err)
	}

	logger.Debug("立即转发任务发送成功", "task_id", task.ID)
	return task.ID, nil
}

// sendMessageDirectly 直接发送消息到Discord（用于立即转发）
func (frs *ForwardRuleService) sendMessageDirectly(ctx context.Context, task *message_forward.MessageForwardTask) error {
	if frs.discordClient == nil {
		return fmt.Errorf("Discord客户端未设置")
	}

	session := frs.discordClient.Session
	if session == nil {
		return fmt.Errorf("Discord会话未建立")
	}

	successCount := 0
	var lastError error

	for _, channelID := range task.TargetChannels {
		logger.Debug("直接发送消息到频道", "channel", channelID, "task_id", task.ID)

		// 优先处理ProductItem（使用通知服务）
		if len(task.Products) > 0 {
			// 如果有通知服务，使用通知服务发送产品通知
			if notificationService := frs.getNotificationService(); notificationService != nil {
				if err := frs.sendProductNotificationsDirect(ctx, notificationService, task.Products, channelID); err != nil {
					logger.Error("直接发送产品通知失败", "error", err, "channel", channelID, "task_id", task.ID)
					lastError = err
					continue
				}
			} else {
				logger.Warn("通知服务不可用，跳过产品消息发送", "task_id", task.ID, "channel", channelID)
				continue
			}
		} else {
			// 处理普通文本消息
			content := task.ProcessedMessage
			if content == "" {
				content = task.OriginalMessage
			}
			if err := frs.sendTextMessageDirect(session, channelID, content); err != nil {
				logger.Error("直接发送文本消息失败", "error", err, "channel", channelID, "task_id", task.ID)
				lastError = err
				continue
			}
		}

		successCount++
		logger.Debug("消息直接发送成功", "channel", channelID, "task_id", task.ID)
	}

	if successCount == 0 {
		return fmt.Errorf("所有频道发送失败，最后错误: %w", lastError)
	}

	if successCount < len(task.TargetChannels) {
		logger.Warn("部分频道发送失败", "success", successCount, "total", len(task.TargetChannels), "task_id", task.ID)
	}

	return nil
}

// sendProductNotificationsDirect 直接发送产品通知
func (frs *ForwardRuleService) sendProductNotificationsDirect(ctx context.Context, notificationService types.ProductNotificationService, products []*types.ProductItem, channelID string) error {
	for _, product := range products {
		if product == nil {
			continue
		}

		// 使用通知服务发送产品通知
		result, err := notificationService.SendProductNotification(ctx, product, channelID)
		if err != nil {
			return fmt.Errorf("发送产品通知失败: %w", err)
		}

		logger.Debug("产品通知直接发送成功",
			"notification_id", result.ID,
			"product_platform", product.Platform,
			"channel", channelID)
	}

	return nil
}

// sendTextMessageDirect 直接发送文本消息
func (frs *ForwardRuleService) sendTextMessageDirect(session *discordgo.Session, channelID, content string) error {
	if content == "" {
		return fmt.Errorf("消息内容为空")
	}

	_, err := session.ChannelMessageSend(channelID, content)
	if err != nil {
		return fmt.Errorf("发送Discord消息失败: %w", err)
	}

	return nil
}

// getNotificationService 获取通知服务（如果可用）
func (frs *ForwardRuleService) getNotificationService() types.ProductNotificationService {
	// 这里可以通过依赖注入或服务发现获取通知服务
	// 暂时返回nil，表示通知服务不可用
	// TODO: 实现通知服务的获取逻辑
	return nil
}

// updateStats 更新统计信息
func (frs *ForwardRuleService) updateStats(ruleName string, success bool) {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if stats, exists := frs.stats[ruleName]; exists {
		if success {
			stats.ForwardedMessages++
		} else {
			stats.ErrorMessages++
		}
		stats.LastForwardTime = time.Now()
	}
}
